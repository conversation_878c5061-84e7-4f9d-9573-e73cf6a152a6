import React, { useState, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../integrations/supabase/client';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Progress } from '../components/ui/progress';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Upload, Download, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { toast } from '../hooks/use-toast';

interface ImportStatus {
  collection: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  message: string;
  recordsProcessed: number;
  totalRecords: number;
}

const Migration: React.FC = () => {
  const { user } = useAuth();
  const [importStatuses, setImportStatuses] = useState<ImportStatus[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  const collections = [
    {
      key: 'aiChats',
      title: 'AI Chats',
      description: 'Your AI conversation history and shared chats',
      filename: 'isotope-ai-chats.csv'
    },
    {
      key: 'groups',
      title: 'Groups & Messages',
      description: 'Your group chats and messages',
      filename: 'isotope-groups-messages.csv'
    },
    {
      key: 'todos',
      title: 'Todos & Tasks',
      description: 'Your todo lists and task management data',
      filename: 'isotope-todos.csv'
    },
    {
      key: 'subjects',
      title: 'Subjects',
      description: 'Your custom subjects and study categories',
      filename: 'isotope-subjects.csv'
    },
    {
      key: 'exams',
      title: 'Exams',
      description: 'Your exam countdown and scheduling data',
      filename: 'isotope-exams.csv'
    },
    {
      key: 'userData',
      title: 'User Profile & Analytics',
      description: 'Your profile, study sessions, and mock test data',
      filename: 'isotope-user-data.csv'
    }
  ];

  const updateImportStatus = (collection: string, updates: Partial<ImportStatus>) => {
    setImportStatuses(prev => {
      const existing = prev.find(s => s.collection === collection);
      if (existing) {
        return prev.map(s => s.collection === collection ? { ...s, ...updates } : s);
      } else {
        return [...prev, {
          collection,
          status: 'pending',
          progress: 0,
          message: '',
          recordsProcessed: 0,
          totalRecords: 0,
          ...updates
        }];
      }
    });
  };

  const parseCSV = (csvText: string): any[] => {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = [];
      let current = '';
      let inQuotes = false;

      for (let j = 0; j < lines[i].length; j++) {
        const char = lines[i][j];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current.trim());

      if (values.length === headers.length) {
        const row: any = {};
        headers.forEach((header, index) => {
          let value = values[index]?.replace(/^"|"$/g, '').replace(/""/g, '"') || '';
          
          // Parse JSON fields
          if (header.includes('messages') || header.includes('tags') || header.includes('members') ||
              header.includes('comments') || header.includes('subject_marks') || header.includes('stats') ||
              header.includes('progress') || header.includes('studySessions') || header.includes('mockTests') ||
              header.includes('replies') || header.includes('mentioned_user_ids') || header.includes('metadata')) {
            try {
              value = value ? JSON.parse(value) : null;
            } catch (e) {
              console.warn(`Failed to parse JSON for ${header}:`, value);
              value = null;
            }
          }

          // Parse boolean fields
          if (header.includes('is_') || header === 'completed' || header === 'welcomeEmailSent') {
            value = value === 'true' || value === '1';
          }

          // Parse numeric fields
          if (header.includes('count') || header.includes('marks') || header === 'duration' ||
              header === 'progress' || header === 'priority') {
            value = value ? parseInt(value) || 0 : 0;
          }

          row[header] = value;
        });
        data.push(row);
      }
    }

    return data;
  };

  const importAIChats = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.userId === user?.uid);

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const chatData = {
        id: row.id,
        userId: row.userId, // Use camelCase (matches Supabase)
        title: row.title || null,
        messages: row.messages || [],
        createdAt: row.createdAt || new Date().toISOString(), // Use camelCase (matches Supabase)
        updatedAt: row.updatedAt || new Date().toISOString(), // Use camelCase (matches Supabase)
        isPublic: row.isPublic || false, // Use camelCase (matches Supabase)
        viewCount: row.viewCount || 0, // Use camelCase (matches Supabase)
        createdBy: row.userId, // Use camelCase (matches Supabase)
        slug: row.slug || null,
        preview: row.preview || null,
        status: row.status || 'approved',
        tags: row.tags || [],
        isPinned: row.isPinned || false, // Use camelCase (matches Supabase)
        isStarred: row.isStarred || false // Use camelCase (matches Supabase)
      };

      const { data: insertedData, error } = await supabase
        .from('aiChats')
        .upsert(chatData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing AI chat:', error);
        console.error('Chat data that failed:', chatData);
        throw error;
      }

      console.log(`Successfully imported AI chat ${row.id}`);

      // Comments are stored as JSON in the aiChats table, no separate table needed
      // They're already included in the chatData.comments field above

      updateImportStatus('aiChats', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} AI chats`
      });
    }
  };

  const importGroups = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.members?.includes(user?.uid));

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const groupData = {
        id: row.id,
        name: row.name,
        description: row.description || null,
        members: row.members || [],
        createdBy: row.createdBy, // Use camelCase (matches Supabase)
        createdAt: row.createdAt || new Date().toISOString(), // Use camelCase (matches Supabase)
        isPublic: row.isPublic || false, // Use camelCase (matches Supabase)
        inviteCode: row.inviteCode || null // Use camelCase (matches Supabase)
      };

      const { error: groupError } = await supabase
        .from('groups')
        .upsert(groupData, { onConflict: 'id' });

      if (groupError) {
        console.error('Error importing group:', groupError);
        throw groupError;
      }

      // Messages are likely stored in a separate chats table or as JSON
      // For now, skip individual message import as there's no messages table in the schema
      console.log(`Imported group ${row.id} with ${row.messages?.length || 0} messages (stored as JSON)`);

      updateImportStatus('groups', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} groups`
      });
    }
  };

  const importTodos = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.createdBy === user?.uid);

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const todoData = {
        id: row.id,
        title: row.title,
        description: row.description || null,
        priority: row.priority || 'medium',
        createdAt: row.createdAt, // Use camelCase (matches Supabase) - note: this is bigint in Supabase
        updatedAt: row.updatedAt, // Use camelCase (matches Supabase) - note: this is bigint in Supabase
        dueDate: row.dueDate || null, // Use camelCase (matches Supabase) - note: this is bigint in Supabase
        assignedTo: row.assignedTo || null, // Use camelCase (matches Supabase)
        assignedToName: row.assignedToName || null, // Use camelCase (matches Supabase)
        assignedToPhotoUrl: row.assignedToPhotoUrl || null, // Use camelCase (matches Supabase)
        createdBy: row.createdBy, // Use camelCase (matches Supabase)
        groupId: row.groupId || null, // Use camelCase (matches Supabase)
        columnId: row.columnId || 'column-1' // Use camelCase (matches Supabase)
      };

      const { error } = await supabase
        .from('todos')
        .upsert(todoData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing todo:', error);
        throw error;
      }

      updateImportStatus('todos', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} todos`
      });
    }
  };

  const importSubjects = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.userId === user?.uid);

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const subjectData = {
        id: row.id,
        userId: row.userId, // Use camelCase (matches Supabase)
        name: row.name,
        color: row.color || '#000000',
        createdAt: row.createdAt || new Date().toISOString() // Use camelCase (matches Supabase)
      };

      const { error } = await supabase
        .from('userSubjects')
        .upsert(subjectData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing subject:', error);
        throw error;
      }

      updateImportStatus('subjects', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} subjects`
      });
    }
  };

  const importExams = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.userId === user?.uid);

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const examData = {
        id: row.id,
        userId: row.userId, // Use camelCase (matches Supabase)
        name: row.name,
        date: row.date,
        totalMarks: row.totalMarks || 0, // Use camelCase (matches Supabase)
        totalMarksObtained: row.totalMarksObtained || 0, // Use camelCase (matches Supabase)
        subjectMarks: row.subjectMarks || [], // Use camelCase (matches Supabase)
        notes: row.notes || '',
        createdAt: row.createdAt || new Date().toISOString() // Use camelCase (matches Supabase)
      };

      const { error } = await supabase
        .from('exams')
        .upsert(examData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing exam:', error);
        throw error;
      }

      updateImportStatus('exams', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} exams`
      });
    }
  };

  const importUserData = async (data: any[]) => {
    const userProfileData = data.find(row => row.type === 'user_profile' && (row.userId === user?.uid || row.id === user?.uid));
    const studySessions = data.filter(row => row.type === 'study_session' && row.userId === user?.uid);
    const mockTests = data.filter(row => row.type === 'mock_test' && row.userId === user?.uid);

    let processed = 0;
    const total = 1; // Only counting user profile since sessions and tests are stored as JSON

    // Import user profile with study sessions and mock tests as JSON
    if (userProfileData) {
      // Convert study sessions array to object format for storage
      const studySessionsObj = {};
      studySessions.forEach(session => {
        studySessionsObj[session.id] = {
          subject: session.subject,
          duration: session.duration,
          mode: session.mode,
          phase: session.phase,
          completed: session.completed,
          startTime: session.startTime,
          endTime: session.endTime,
          notes: session.notes
        };
      });

      // Convert mock tests array to object format for storage
      const mockTestsObj = {};
      mockTests.forEach(test => {
        mockTestsObj[test.id] = {
          name: test.name,
          date: test.date,
          subjectMarks: test.subjectMarks,
          totalMarksObtained: test.totalMarksObtained,
          totalMarks: test.totalMarks,
          notes: test.notes,
          createdAt: test.createdAt
        };
      });

      const profileData = {
        id: user?.uid,
        email: userProfileData.email,
        displayName: userProfileData.displayName, // Use correct field name from export
        photoURL: userProfileData.photoURL, // Use correct field name from export
        username: userProfileData.username,
        created_at: userProfileData.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        lastLogin: userProfileData.lastLogin || new Date().toISOString(), // Use correct field name from export
        welcomeEmailSent: userProfileData.welcomeEmailSent || false, // Use correct field name from export
        backgroundImage: userProfileData.backgroundImage || '', // Use correct field name from export
        bio: userProfileData.bio || '',
        location: userProfileData.location || '',
        stats: userProfileData.stats || {},
        progress: userProfileData.progress || {},
        studySessions: Object.keys(studySessionsObj).length > 0 ? studySessionsObj : (userProfileData.studySessions || {}), // Use imported sessions or existing
        mockTests: Object.keys(mockTestsObj).length > 0 ? mockTestsObj : (userProfileData.mockTests || {}) // Use imported tests or existing
      };

      const { error } = await supabase
        .from('users')
        .upsert(profileData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing user profile:', error);
        console.error('Profile data that failed:', profileData);
        throw error;
      }

      console.log(`Successfully imported user profile for ${user?.uid} with ${studySessions.length} study sessions and ${mockTests.length} mock tests`);
    }
    processed++;

    updateImportStatus('userData', {
      progress: 100,
      recordsProcessed: processed,
      totalRecords: total,
      message: `Imported user profile with ${studySessions.length} study sessions and ${mockTests.length} mock tests`
    });
  };

  const handleFileUpload = async (collectionKey: string, file: File) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to import data",
        variant: "destructive"
      });
      return;
    }

    updateImportStatus(collectionKey, {
      status: 'processing',
      progress: 0,
      message: 'Reading file...'
    });

    try {
      const text = await file.text();
      const data = parseCSV(text);

      if (data.length === 0) {
        throw new Error('No valid data found in CSV file');
      }

      updateImportStatus(collectionKey, {
        message: `Found ${data.length} records, starting import...`,
        totalRecords: data.length
      });

      switch (collectionKey) {
        case 'aiChats':
          await importAIChats(data);
          break;
        case 'groups':
          await importGroups(data);
          break;
        case 'todos':
          await importTodos(data);
          break;
        case 'subjects':
          await importSubjects(data);
          break;
        case 'exams':
          await importExams(data);
          break;
        case 'userData':
          await importUserData(data);
          break;
        default:
          throw new Error(`Unknown collection: ${collectionKey}`);
      }

      updateImportStatus(collectionKey, {
        status: 'completed',
        progress: 100,
        message: `Successfully imported ${data.length} records`
      });

      toast({
        title: "Import Successful",
        description: `${collectionKey} data has been imported successfully`
      });

    } catch (error: any) {
      console.error(`Import error for ${collectionKey}:`, error);
      updateImportStatus(collectionKey, {
        status: 'error',
        message: `Import failed: ${error.message}`
      });

      toast({
        title: "Import Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const triggerFileInput = (collectionKey: string) => {
    fileInputRefs.current[collectionKey]?.click();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'processing':
        return <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <Upload className="h-5 w-5 text-gray-400" />;
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access the migration tool
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Data Migration</h1>
          <p className="text-gray-600 mb-6">
            Import your data from the legacy Firebase system to the new Supabase backend
          </p>

          <Alert className="mb-6">
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Before you start:</strong> Make sure you have exported your data from the legacy system at{' '}
              <a href="https://legacy.isotopeai.in" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                legacy.isotopeai.in
              </a>
            </AlertDescription>
          </Alert>
        </div>

        <div className="grid gap-6">
          {collections.map((collection) => {
            const status = importStatuses.find(s => s.collection === collection.key);

            return (
              <Card key={collection.key}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {getStatusIcon(status?.status || 'pending')}
                        {collection.title}
                      </CardTitle>
                      <CardDescription>{collection.description}</CardDescription>
                    </div>
                    <Button
                      onClick={() => triggerFileInput(collection.key)}
                      disabled={isImporting || status?.status === 'processing'}
                      variant={status?.status === 'completed' ? 'outline' : 'default'}
                    >
                      {status?.status === 'completed' ? 'Re-import' : 'Import'}
                    </Button>
                  </div>
                </CardHeader>

                {status && (
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{status.message}</span>
                        <span>{status.recordsProcessed}/{status.totalRecords}</span>
                      </div>
                      <Progress value={status.progress} className="w-full" />
                    </div>
                  </CardContent>
                )}

                <input
                  ref={el => fileInputRefs.current[collection.key] = el}
                  type="file"
                  accept=".csv"
                  style={{ display: 'none' }}
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFileUpload(collection.key, file);
                    }
                  }}
                />
              </Card>
            );
          })}
        </div>

        <div className="mt-8 text-center">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> This migration process will merge your legacy data with any existing data in the new system.
              Existing records with the same ID will be updated. Make sure to backup your current data before proceeding.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    </div>
  );
};

export default Migration;
