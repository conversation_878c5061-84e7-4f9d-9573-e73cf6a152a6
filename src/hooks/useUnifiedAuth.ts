import { useAuth } from '../contexts/AuthContext';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useUserStore } from '../stores/userStore';
import { User as FirebaseUser } from 'firebase/auth';
import { User as SupabaseUser } from '@supabase/supabase-js';

// Normalized user interface that works with both Firebase and Supabase
export interface UnifiedUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified?: boolean;
}

/**
 * Unified auth hook that works with both Firebase and Supabase auth providers
 * This hook automatically selects the correct auth provider based on the current configuration
 */
export function useUnifiedAuth() {
  const { authProvider } = useUserStore();

  // Helper function to normalize user object
  const normalizeUser = (user: FirebaseUser | SupabaseUser | null): UnifiedUser | null => {
    if (!user) return null;

    // Check if it's a Supabase user
    if ('id' in user) {
      const supabaseUser = user as SupabaseUser;
      return {
        uid: supabaseUser.id,
        email: supabaseUser.email || null,
        displayName: supabaseUser.user_metadata?.full_name || supabaseUser.user_metadata?.name || null,
        photoURL: supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture || null,
        emailVerified: supabaseUser.email_confirmed_at ? true : false
      };
    } else {
      // Firebase user
      const firebaseUser = user as FirebaseUser;
      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified
      };
    }
  };

  // Use the appropriate auth hook based on the provider
  if (authProvider === 'supabase') {
    try {
      const supabaseAuth = useSupabaseAuth();
      return {
        user: normalizeUser(supabaseAuth.user),
        userProfile: supabaseAuth.userProfile,
        loading: supabaseAuth.loading,
        signInWithGoogle: supabaseAuth.signInWithGoogle,
        signInWithEmailPassword: supabaseAuth.signInWithEmailPassword,
        signUpWithEmailPassword: supabaseAuth.signUpWithEmailPassword,
        signOut: supabaseAuth.signOut,
        linkToExistingData: supabaseAuth.linkToExistingData,
        authProvider: 'supabase' as const
      };
    } catch {
      // Fallback to Firebase auth if Supabase context is not available
      const firebaseAuth = useAuth();
      return {
        user: normalizeUser(firebaseAuth.user),
        userProfile: null, // Firebase auth doesn't have userProfile in the same format
        loading: firebaseAuth.loading,
        signInWithGoogle: firebaseAuth.signInWithGoogle,
        signInWithEmailPassword: firebaseAuth.signInWithEmailPassword,
        signUpWithEmailPassword: firebaseAuth.signUpWithEmailPassword,
        signOut: firebaseAuth.signOut,
        linkToExistingData: undefined,
        authProvider: 'firebase' as const
      };
    }
  } else {
    const firebaseAuth = useAuth();
    return {
      user: normalizeUser(firebaseAuth.user),
      userProfile: null, // Firebase auth doesn't have userProfile in the same format
      loading: firebaseAuth.loading,
      signInWithGoogle: firebaseAuth.signInWithGoogle,
      signInWithEmailPassword: firebaseAuth.signInWithEmailPassword,
      signUpWithEmailPassword: firebaseAuth.signUpWithEmailPassword,
      signOut: firebaseAuth.signOut,
      linkToExistingData: undefined,
      authProvider: 'firebase' as const
    };
  }
}
