import { Link, useLocation } from "react-router-dom";
import { Menu, X } from "lucide-react";
import { useState } from "react";
import { Button } from "./ui/button";
import { useUnifiedAuth } from "@/hooks/useUnifiedAuth";
import { cn } from "@/lib/utils";

const navItems = [
  { path: "/", label: "Home" },
  { path: "/ai-landing", label: "AI" },
  { path: "/groups-landing", label: "Groups" },
  { path: "/productivity-landing", label: "Productivity" },
  { path: "/tasks-landing", label: "Tasks" },
  { path: "/about-us", label: "About Us" },
  { path: "/contact-us", label: "Contact" },
  { path: "https://isotopeai.featurebase.app/changelog", label: "Changelog", external: true },
];

export const LandingNav = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user } = useUnifiedAuth();
  const location = useLocation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="w-full py-4 px-6 backdrop-blur-md bg-background/70 border-b border-white/10 sticky top-0 z-50">
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center gap-2">
            <div className="bg-primary/20 p-2 rounded-full">
              <img src="/favicon.ico" alt="IsotopeAI Logo" className="w-5 h-5" />
            </div>
            <span className="font-bold text-xl">IsotopeAI</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-8">
            {navItems.map((page) => (
              page.external ? (
                <a
                  key={page.path}
                  href={page.path}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={cn(
                    "text-sm font-medium transition-colors",
                    "hover:text-primary text-foreground/80"
                  )}
                >
                  {page.label}
                </a>
              ) : (
                <Link
                  key={page.path}
                  to={page.path}
                  className={cn(
                    "text-sm font-medium transition-colors",
                    location.pathname === page.path
                      ? "text-primary"
                      : "hover:text-primary text-foreground/80"
                  )}
                >
                  {page.label}
                </Link>
              )
            ))}
          </nav>

          <div className="hidden md:flex items-center">
            {user && (
              <Button
                variant="default"
                size="sm"
                asChild
              >
                <Link to="/ai">Go to Dashboard</Link>
              </Button>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2 rounded-md hover:bg-white/5"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-md border-b border-white/10 p-4 z-50">
          <nav className="flex flex-col space-y-4">
            {navItems.map((page) => (
              page.external ? (
                <a
                  key={page.path}
                  href={page.path}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={cn(
                    "text-sm font-medium transition-colors p-2 rounded-md hover:bg-white/5"
                  )}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {page.label}
                </a>
              ) : (
                <Link
                  key={page.path}
                  to={page.path}
                  className={cn(
                    "text-sm font-medium transition-colors p-2 rounded-md",
                    location.pathname === page.path
                      ? "bg-white/10 text-primary"
                      : "hover:bg-white/5"
                  )}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {page.label}
                </Link>
              )
            ))}
            {user && (
              <Button
                variant="default"
                size="sm"
                asChild
                className="mt-2"
              >
                <Link to="/ai" onClick={() => setIsMenuOpen(false)}>Go to Dashboard</Link>
              </Button>
            )}
          </nav>
        </div>
      )}
    </header>
  );
};