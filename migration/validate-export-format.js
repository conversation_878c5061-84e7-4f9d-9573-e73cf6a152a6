// Validation script to check if export format matches Supabase schema
// This script helps verify that the exported CSV data has the correct field names

const expectedSchemas = {
  users: [
    'id', 'uid', 'email', 'displayName', 'photoURL', 'username',
    'created_at', 'lastLogin', 'updated_at', 'welcomeEmailSent',
    'backgroundImage', 'bio', 'location', 'stats', 'progress',
    'studySessions', 'mockTests'
  ],

  aiChats: [
    'id', 'userId', 'createdBy', 'title', 'slug', 'messages',
    'createdAt', 'updatedAt', 'isPublic', 'viewCount', 'preview',
    'comments', 'isPinned', 'isStarred', 'status', 'tags'
  ],

  groups: [
    'id', 'name', 'description', 'members', 'createdBy', 'createdAt',
    'isPublic', 'inviteCode'
  ],

  todos: [
    'id', 'title', 'description', 'priority', 'createdAt', 'updatedAt',
    'dueDate', 'assignedTo', 'assignedToName', 'assignedToPhotoUrl',
    'createdBy', 'groupId', 'columnId'
  ],

  userSubjects: [
    'id', 'userId', 'name', 'color', 'createdAt'
  ],

  exams: [
    'id', 'userId', 'name', 'date', 'totalMarks', 'totalMarksObtained',
    'subjectMarks', 'notes', 'createdAt'
  ],

  // Note: study_sessions and mock_tests are stored as JSON in users table
  study_sessions_table: [
    'id', 'userId', 'subject', 'duration', 'mode', 'phase', 'completed',
    'startTime', 'endTime', 'notes', 'createdAt'
  ],

  mock_tests_table: [
    'id', 'userId', 'name', 'date', 'subjectMarks',
    'totalMarksObtained', 'totalMarks', 'notes', 'createdAt'
  ]
};

function validateCSVHeaders(csvContent, expectedSchema, tableName) {
  const lines = csvContent.split('\n');
  if (lines.length === 0) {
    console.error(`❌ ${tableName}: CSV is empty`);
    return false;
  }
  
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const missing = expectedSchema.filter(field => !headers.includes(field));
  const extra = headers.filter(field => !expectedSchema.includes(field));
  
  if (missing.length === 0 && extra.length === 0) {
    console.log(`✅ ${tableName}: All fields match perfectly`);
    return true;
  }
  
  if (missing.length > 0) {
    console.warn(`⚠️  ${tableName}: Missing fields: ${missing.join(', ')}`);
  }
  
  if (extra.length > 0) {
    console.warn(`⚠️  ${tableName}: Extra fields: ${extra.join(', ')}`);
  }
  
  console.log(`📋 ${tableName}: Expected: ${expectedSchema.join(', ')}`);
  console.log(`📋 ${tableName}: Actual: ${headers.join(', ')}`);
  
  return missing.length === 0; // Only fail if missing required fields
}

// Example usage:
// const csvContent = "id,user_id,title,created_at\n1,user123,Test Chat,2024-01-01T00:00:00Z";
// validateCSVHeaders(csvContent, expectedSchemas.ai_chats, 'ai_chats');

console.log('🔍 Export Format Validation Schema');
console.log('Use this script to validate your exported CSV files match Supabase schema');
console.log('');
console.log('Expected schemas:');
Object.entries(expectedSchemas).forEach(([table, fields]) => {
  console.log(`${table}: ${fields.join(', ')}`);
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { expectedSchemas, validateCSVHeaders };
}
