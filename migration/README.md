# IsotopeAI Data Migration Guide

This guide explains how to export data from the legacy Firebase site and import it into the new Supabase-powered application.

## Overview

The migration process involves two main steps:
1. **Export**: Extract data from Firebase in the correct format for Supabase
2. **Import**: Upload the exported CSV files to the new application

## Export Process

### 1. Access the Legacy Export Tool

Open the legacy export tool at: `migration/legacy-export/index.html`

### 2. Sign In

- Click "Sign In with Google"
- Use the same Google account that you used on the legacy IsotopeAI site
- Only the admin account can export all users' data

### 3. Export Your Data

The tool will export data in the following files:

- **isotope-ai-chats.csv** - Your AI conversation history
- **isotope-groups-messages.csv** - Group chats and messages  
- **isotope-todos.csv** - Todo lists and tasks
- **isotope-subjects.csv** - Custom subjects
- **isotope-exams.csv** - Exam schedules
- **isotope-user-data.csv** - Profile, study sessions, and mock tests

### 4. Admin Export (Admin Only)

If you're an admin, you can export all users' data:
- Click "Export All Admin Data"
- This exports data for all users in the system

## Import Process

### 1. Access the Migration Page

In the new IsotopeAI application:
- Sign in with your Google account
- Navigate to the Migration page
- Upload the CSV files you exported

### 2. Upload Files

For each data type:
- Click "Choose File" 
- Select the corresponding CSV file
- Click "Import" to start the process

### 3. Monitor Progress

- Watch the progress bars for each import
- Check for any error messages
- Successful imports will show green checkmarks

## Data Format

The export tool generates data in the exact camelCase format required by Supabase:

### Field Names (All CamelCase)

Supabase tables use camelCase field names, exactly matching Firebase:
- `userId`, `createdAt`, `updatedAt`
- `isPublic`, `viewCount`, `isPinned`, `isStarred`
- `createdBy`, `groupId`, `totalMarks`, `subjectMarks`
- `assignedTo`, `assignedToName`, `assignedToPhotoUrl`
- `inviteCode`, `columnId`, `dueDate`

**Note**: Only `created_at` and `updated_at` in the users table use snake_case.

### User Profile Fields

The user profile export includes all fields that match the Supabase users table:
- `displayName`, `photoURL`, `lastLogin`, `welcomeEmailSent`
- `backgroundImage`, `bio`, `location`
- `stats`, `progress`, `studySessions`, `mockTests` (as JSON)

## Troubleshooting

### Export Issues

1. **Sign-in fails**: Make sure you're using the correct Google account
2. **No data exported**: Check that you have data in the legacy system
3. **Admin export fails**: Verify you have admin permissions

### Import Issues

1. **Import shows success but no data**: Check the browser console for errors
2. **Field validation errors**: Ensure the CSV format matches the expected schema
3. **Permission errors**: Make sure you're signed in to the new application

### Validation

Use the validation script to check your export format:
```javascript
// In browser console or Node.js
const { validateCSVHeaders, expectedSchemas } = require('./validate-export-format.js');
validateCSVHeaders(csvContent, expectedSchemas.ai_chats, 'ai_chats');
```

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify the CSV file format matches the expected schema
3. Contact support with specific error messages and steps to reproduce

## Security Notes

- The export tool only exports data for the signed-in user (unless admin)
- All data is processed locally in your browser
- CSV files contain your personal data - handle them securely
- Delete exported files after successful import
